#ifndef __DAC_H
#define __DAC_H
#include "sys.h"
#include "stm32f4xx.h"
#include "delay.h"
#include "math.h"

// DAC正弦波参数
#define DAC_SINE_SAMPLES 256        // 正弦波查找表大小
#define DAC_MAX_FREQ_HZ 100000.0f   // 最大输出频率100kHz
#define DAC_MIN_VOLTAGE 0.0f        // 最小电压0V
#define DAC_MAX_VOLTAGE 3.3f        // 最大电压3.3V
#define DAC_VREF 3.3f               // DAC参考电压3.3V
#define DAC_OFFSET_VOLTAGE 1.65f    // 偏移电压1.65V (正弦波中心电压，3.3V/2)

// 频率-幅度映射表参数
#define DAC_FREQ_TABLE_SIZE 30      // 频率表大小
#define DAC_BASE_FREQ 100.0f        // 基础频率100Hz
#define DAC_FREQ_STEP 100.0f        // 频率步进100Hz
#define DAC_AMPLITUDE_SCALE 3.9f    // 幅度缩放因子

// 外部变量声明
extern float dac_output_frequency;
extern uint8_t dac_output_enabled;
extern uint8_t dac_user_enabled;
extern float dac_amplitude_multiplier;

// DAC初始化函数
void DAC_PA4_Init(void);
void DAC_SineWave_Init(void);
void DAC_DMA_Init(void);

// DAC输出函数
void DAC_SetChannel1Value(uint16_t value);
void DAC_SetChannel1Voltage(float voltage);

// DAC正弦波控制函数
void DAC_SetSineFrequency(float frequency);
void DAC_StartSineOutput(void);
void DAC_StopSineOutput(void);
void DAC_UpdateSineOutput(void);
float DAC_GetAmplitudeForFrequency(float frequency);  // 根据频率获取幅度

// DAC幅度倍数控制函数
void DAC_SetAmplitudeMultiplier(float multiplier);
void DAC_NextAmplitudeMultiplier(void);
float DAC_GetAmplitudeMultiplier(void);

// DAC谐波合成函数
typedef struct {
    float frequency;    // 谐波频率 (Hz)
    float amplitude;    // 谐波电压幅度 (V)
    float phase;        // 谐波相位 (弧度)
} HarmonicComponent_t;

void DAC_GenerateMultiFrequencySignal(HarmonicComponent_t* harmonics, int num_harmonics);
void DAC_GenerateSingleFrequencySignal(float frequency, float amplitude);  // 单频率输出
void DAC_StopHarmonicOutput(void);

// DAC用户使能控制函数
void DAC_SetUserEnable(uint8_t enable);
uint8_t DAC_GetUserEnable(void);

// DAC使能/失能函数
void DAC_Enable(void);
void DAC_Disable(void);

// DAC重构信号输出函数
void DAC_StartReconstructedOutput(float sample_rate);
void DAC_StartOptimizedReconstructedOutput(float sample_rate, uint16_t* data_buffer, uint16_t buffer_size);
void DAC_StartSyncReconstructedOutput(float sample_rate, uint16_t* data_buffer, uint16_t buffer_size);  // 同步版本
void DAC_StopReconstructedOutput(void);

// DAC测试函数
void DAC_Test(void);
void DAC_SimpleTest(void);

#endif
